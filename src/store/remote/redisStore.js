const redis = require('redis')

const { REDIS_CONNECTION_STRING } = require('../../conf')

const clientPromise = new Promise((resolve, reject) => {
  const prefix = 'sockets-'
  const client = redis.createClient(REDIS_CONNECTION_STRING, { prefix })
  client.on('connect', () => resolve(client))
  client.on('error', reject)
})

const promisify = async (func, id, socketId, resMap) => {
  const client = await clientPromise

  return new Promise((resolve, reject) => {
    const callback = (err, reply) =>
      err ? reject(err) : resolve(resMap ? resMap(reply) : reply)

    return socketId
      ? client[func](`${id}`, socketId, callback)
      : client[func](`${id}`, callback)
  })
}

const isAvailable = id => promisify('exists', id, null, res => res === 1)

const getUser = id =>
  promisify('smembers', id, null, res => (res && res.length ? res : null))

const addUser = (id, socketId) => promisify('sadd', id, socketId)

const removeUser = (id, socketId) => promisify('srem', id, socketId)

module.exports = {
  isAvailable,
  getUser,
  addUser,
  removeUser,
}
