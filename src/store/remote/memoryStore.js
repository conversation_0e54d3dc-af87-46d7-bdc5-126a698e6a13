const users = new Map()

const isAvailable = id => {
  return users.has(id)
}

const getUser = id => {
  const res = users.get(id)
  return res ? [...res] : null
}

const addUser = (id, socketId) => {
  if (isAvailable(id)) {
    users.get(id).add(socketId)
  } else {
    users.set(id, new Set([socketId]))
  }
}

const removeUser = (id, socketId) => {
  if (isAvailable(id)) {
    users.get(id).delete(socketId)
    if (!users.get(id).size) {
      users.delete(id)
      return null
    }
  }
}

module.exports = {
  isAvailable,
  getUser,
  addUser,
  removeUser,
}
