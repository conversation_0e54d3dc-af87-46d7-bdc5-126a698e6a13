const { map } = require('lodash')

const { sendMessage } = require('../socket')
const ConversationNotificationType = require('../../../models/ConversationNotificationType')

module.exports = async function sendConversationItem(
  { ctx, log },
  { conversationId, conversationItem: { recipients, ...conversationItem } },
) {
  await Promise.all(
    map(recipients, recipientId => {
      const message = {
        ...conversationItem,
        recipientId,
        conversationId,
        notificationType: ConversationNotificationType.UPDATED,
      }
      sendMessage(
        { socket: ctx.app.io, log },
        message,
        `conversation/${conversationId}`,
      )
      sendMessage(
        { socket: ctx.app.io, log },
        {
          recipientId,
          conversationId,
          notificationType: ConversationNotificationType.CREATED,
        },
        'conversation',
      )
    }),
  )

  return {
    isSent: true,
  }
}
