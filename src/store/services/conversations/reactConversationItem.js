const { map } = require('lodash')

const { sendMessage } = require('../socket')
const ConversationNotificationType = require('../../../models/ConversationNotificationType')

module.exports = async function reactConversationItem(
  { ctx, log },
  {
    conversationId,
    reactConversationInfo: {
      recipients,
      reactionType,
      conversationItemId,
      participantPersonId,
    },
  },
) {
  await Promise.all(
    map(recipients, recipientId =>
      sendMessage(
        { socket: ctx.app.io, log },
        {
          reactionType,
          conversationId,
          participantPersonId,
          conversationItemId,
          recipientId,
          notificationType: ConversationNotificationType.REACTED,
        },
        `conversation/${conversationId}`,
      ),
    ),
  )

  return {
    isSent: true,
  }
}
