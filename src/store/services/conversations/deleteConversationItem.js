const { map } = require('lodash')

const { sendMessage } = require('../socket')
const ConversationNotificationType = require('../../../models/ConversationNotificationType')

module.exports = async function deleteConversationItem(
  { ctx, log },
  {
    conversationId,
    deleteConversationInfo: { recipients, conversationItemId },
  },
) {
  await Promise.all(
    map(recipients, recipientId =>
      sendMessage(
        { socket: ctx.app.io, log },
        {
          conversationId,
          conversationItemId,
          recipientId,
          notificationType: ConversationNotificationType.DELETED,
        },
        `conversation/${conversationId}`,
      ),
    ),
  )

  return {
    isSent: true,
  }
}
