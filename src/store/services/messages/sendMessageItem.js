const { map } = require('lodash')

const { sendMessage } = require('../socket')

module.exports = async function sendMessageItem(
  { ctx, log },
  { messageId, messageItem: { recipients, ...messageItem } },
) {
  await Promise.all(
    map(recipients, recipientId =>
      sendMessage(
        { socket: ctx.app.io, log },
        {
          ...messageItem,
          recipientId,
          messageId,
        },
        'message',
      ),
    ),
  )

  return {
    isSent: true,
  }
}
