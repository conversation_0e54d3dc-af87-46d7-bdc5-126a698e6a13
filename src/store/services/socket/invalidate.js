const { map, forEach, filter } = require('lodash')

const { getUser, removeUser } = require('../../remote/socketStore')

const invalidate = async (socket, userId) => {
  const socketIds = await getUser(userId)

  if (socketIds) {
    let to = socket
    forEach(socketIds, socketId => (to = to.to(socketId)))

    await new Promise(resolve => {
      to.clients((error, clients) => {
        const clientsSet = new Set(clients)
        resolve(
          Promise.all(
            map(
              filter(socketIds, socketId => !clientsSet.has(socketId)),
              socketId => removeUser(userId, socketId),
            ),
          ),
        )
      })
    })
  }
}

module.exports = invalidate
