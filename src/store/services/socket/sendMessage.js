const { forEach } = require('lodash')

const invalidate = require('./invalidate')
const { getUser } = require('../../remote/socketStore')

const senMessage = async ({ socket, log }, notification, topic) => {
  const { recipientId } = notification
  log.info(
    `send ${topic}: for user ${recipientId} with data: ${JSON.stringify(
      notification,
    )}`,
  )

  await invalidate(socket, recipientId)

  const socketIds = await getUser(recipientId)

  if (socketIds) {
    let to = socket
    forEach(socketIds, socketId => (to = to.to(socketId)))
    to.emit(topic, notification)

    return { send: socketIds.length }
  }
  return { send: 0 }
}

module.exports = senMessage
