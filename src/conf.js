const optional = (key, defaultValue) => process.env[key] || defaultValue

const mandatory = key => {
  const value = process.env[key]
  if (!value) {
    throw new Error(`missing mandatory env value: ${key}`)
  }
  return value
}

module.exports = {
  JWT_SECRET: mandatory('JWT_SECRET'),
  COOKIE_SECRET: mandatory('COOKIE_SECRET'),
  REDIS_CONNECTION_STRING: optional('REDIS_CONNECTION_STRING'),
  TEST_ENV: optional('TEST_ENV'),

  AUDIENCE_TEMPLATE: optional('AUDIENCE_TEMPLATE'),
  COOKIE_POSTFIX_TEMPLATE: optional('COOKIE_POSTFIX_TEMPLATE'),
  REDIS_PASSWORD: optional('REDIS_PASSWORD'),
}
