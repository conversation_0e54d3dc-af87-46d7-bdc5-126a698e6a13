const { template } = require('lodash')
const conf = require('../conf')

const applyTemplate = (envTemplate, domain) =>
  envTemplate
    ? template(envTemplate)({
        domain,
      })
    : domain

module.exports = function cookOriginDependedCookie(originDomain) {
  const { DOMAIN_TEMPLATE, COOKIE_POSTFIX_TEMPLATE, AUDIENCE_TEMPLATE } = conf

  const COOKIE_POSTFIX = applyTemplate(COOKIE_POSTFIX_TEMPLATE, originDomain)

  return {
    DOMAIN: applyTemplate(DOMAIN_TEMPLATE, originDomain),
    AUDIENCE: applyTemplate(AUDIENCE_TEMPLATE, originDomain),
    AUTH_KEY: `Authorization_${COOKIE_POSTFIX}`,
    ORIGIN_AUTH_KEY: `OriginalAuthorization_${COOKIE_POSTFIX}`,
    ORIGINAL_UID: `OriginalUID_${COOKIE_POSTFIX}`,
  }
}
