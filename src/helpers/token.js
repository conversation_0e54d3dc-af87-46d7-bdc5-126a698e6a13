const jwt = require('jsonwebtoken')

const { JWT_SECRET } = require('../conf')
const websiteHost = require('../helpers/websiteHost')
const cookOriginDependedCookie = require('../helpers/cookOriginDependedCookie')

const getSecret = () => {
  if (!JWT_SECRET) {
    throw new Error('missing required env variable JWT_SECRET')
  }
  return JWT_SECRET
}

const extractToken = (ctx, token) => {
  const { AUDIENCE } = cookOriginDependedCookie(websiteHost({ ctx }))

  return new Promise((resolve, reject) =>
    jwt.verify(token, getSecret(), { audience: AUDIENCE }, (err, payload) => {
      if (err) {
        if (
          err instanceof jwt.TokenExpiredError ||
          err.message.includes('audience invalid')
        ) {
          resolve(null)
        }
        reject(err)
      } else {
        resolve(payload)
      }
    }),
  )
}

module.exports = {
  extractToken,
}
