const IO = require('koa-socket-2')
const redisAdapter = require('socket.io-redis')
const redis = require('redis')

const withLogger = require('./middlewares/withLogger')
const withCookies = require('./middlewares/withCookies')
const withCookieUserToken = require('./middlewares/withCookieUserToken')
const withUserAuthentication = require('./middlewares/withUserAuthentication')

const service = require('./store/services/socket')

const { REDIS_CONNECTION_STRING, REDIS_PASSWORD } = require('./conf')

const io = new IO()

const attach = app => {
  io.attach(app)

  io.use(withLogger)
  io.use(withCookies)
  io.use(withCookieUserToken)
  io.use(withUserAuthentication)

  io.on('disconnect', service.onDisconnect)
  io.listeners.set('connection', [
    async (packet, id) => {
      if (!io.composed) {
        await service.onConnection(packet, { id })
      } else {
        await io.composed(packet, () => service.onConnection(packet, { id }))
      }
    },
  ])

  io.on('error', console.error)

  if (REDIS_CONNECTION_STRING) {
    const options = REDIS_PASSWORD
      ? {
          password: REDIS_PASSWORD,
          url: REDIS_CONNECTION_STRING,
        }
      : REDIS_CONNECTION_STRING

    const prefix = 'redisAdapter'
    io.adapter(
      redisAdapter({
        pubClient: redis.createClient(options, { prefix }),
        subClient: redis.createClient(options, {
          prefix,
        }),
      }),
    )
  }
}

module.exports = { io, attach }
