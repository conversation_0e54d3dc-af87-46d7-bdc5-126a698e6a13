const WebsService = require('../store/services/WebsService')

const withWebsClient = (serviceName = 'edana_api_webs') => async (
  { state: { requestFramework } },
  next,
) => {
  const getService = async () => {
    const client = await requestFramework.requireServiceClient(serviceName)
    return new WebsService(client)
  }

  let _websClient

  Object.assign(requestFramework, {
    get websClient() {
      _websClient = _websClient || getService()
      return _websClient
    },
  })

  return next()
}

module.exports = withWebsClient
