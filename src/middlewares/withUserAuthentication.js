const { extractToken } = require('../helpers/token')

const withUserAuthentication = async (ctx, next) => {
  if (ctx.state.token) {
    try {
      const payload = await extractToken(ctx, ctx.state.token)
      if (payload && payload.userId) {
        if (payload.userId) {
          ctx.state.userId = payload.userId
        }
        if (payload.userName) {
          ctx.state.userName = payload.userName
        }
      }
    } catch (e) {
      ctx.state.log.warn('invalid jwt token', e)
      ctx.throw(403, 'token invalid')
    }
  } else {
    return
  }
  return next()
}

module.exports = withUserAuthentication
