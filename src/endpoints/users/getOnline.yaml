paths:
  /online:
    get:
      summary: Check online/offline status for users
      tags:
        - users
      operationId: getOnline
      parameters:
        - in: query
          name: userIds
          description: list of user ids to check
          type: array
          items:
            type: integer
            format: int32
      responses:
        200:
          description: map of user online statuses, 'true' for online
          schema:
            type: object
