paths:
  /send-notification/bulk:
    post:
      consumes:
        - application/json
      summary: Send notifications bulk
      tags:
        - notifications
      operationId: sendNotificationsBulk
      parameters:
        - name: notifications
          in: body
          description: Notifications params
          required: true
          schema:
            type: array
            items:
              $ref: '#/definitions/Notification'

      responses:
        200:
          description: Response about sending notifications
          schema:
            type: object

definitions:
  Notification:
    type: object
    required:
      - _id
      - tenantId
      - orgGroupId
      - recipientId
      - subject
      - content
      - createdAt
    properties:
      _id:
        type: string
      _v:
        type: string
      tenantId:
        type: integer
        format: int64
        minimum: 1
      orgGroupId:
        type: integer
        format: int64
        minimum: 1
      notificationId:
        type: [integer, 'null']
        format: int64
        minimum: 1
      recipientId:
        type: integer
        format: int64
        minimum: 1
      categoryId:
        type: integer
        format: int64
        minimum: 1
      senderId:
        type: [integer, 'null']
        format: int64
        minimum: 1
      sender:
        $ref: '#/definitions/Sender'
      icon:
        type: [string, 'null']
      subject:
        type: string
      content:
        type: string
      createdAt:
        type: string
        format: date-time
      attachments:
        type: array
        items:
          $ref: '#/definitions/AttachFileInfo'

  AttachFileInfo:
    type: object
    required:
      - fileId
    properties:
      fileId:
        type: integer
        format: int64
      uploadToken:
        type: string
      description:
        type: string
      fileName:
        type: string

  Sender:
    type: object
    required:
      - firstName
      - lastName
    properties:
      photoFileId:
        type: [integer, 'null']
        format: int64
        minimum: 1
      preferredName:
        type: [string, 'null']
      firstName:
        type: string
      lastName:
        type: string
      gender:
        type: [string, 'null']
