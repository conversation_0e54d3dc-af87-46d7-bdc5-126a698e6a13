paths:
  /send-conversation-message/:conversationId:
    post:
      consumes:
        - application/json
      summary: Send conversation item to conversation
      tags:
        - conversations
      operationId: sendConversationItem
      parameters:
        - in: query
          name: conversationId
          description: Conversation ID to send in
          type: integer
          required: true
          format: int64
        - in: body
          name: conversationItem
          description: completed ConversationItem to send
          required: true
          schema:
            type: object
            $ref: '#/definitions/ConversationItem'
      responses:
        200:
          description: Response about sending conversation message
          schema:
            type: object

definitions:
  ConversationItem:
    type: object
    required:
      - id
      - createdAt
      - content
      - __typename
    properties:
      id:
        type: integer
        format: int64
      conversationParticipantId:
        type: integer
        format: int64
      content:
        type: [string, 'null']
      parentId:
        type: [integer, 'null']
        format: int64
      createdAt:
        type: string
        format: date-time
      updatedAt:
        type: string
        format: date-time
      quotedItemId:
        type: [integer, 'null']
        format: int64
      quotedItemContent:
        type: [string, 'null']
        format: int64

      status:
        type: string
        enum: [ACTIVE, INACTIVE, DELETED]
      recipients:
        type: array
        items:
          type: number
          format: int64
      person:
        type: object
        $ref: '#/definitions/Person'
      attachments:
        type: array
        items:
          type: object
          $ref: '#/definitions/FileAttachment'
      quote:
        type: object
        $ref: '#/definitions/ConversationItem'
      __typename:
        type: string
        enum: [ConversationItem]
