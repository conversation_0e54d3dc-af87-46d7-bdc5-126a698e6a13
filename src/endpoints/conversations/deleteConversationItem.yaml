paths:
  /send-conversation-message/:conversationId/:conversationItemId:
    delete:
      consumes:
        - application/json
      summary: Delete Conversation Item
      tags:
        - conversations
      operationId: deleteConversationItem
      parameters:
        - in: query
          name: conversationId
          description: Conversation ID to send in
          type: integer
          required: true
          format: int64
        - in: body
          name: deleteConversationInfo
          description: Short deleted conversation item info
          required: true
          schema:
            type: object
            $ref: '#/definitions/DeleteConversationInfo'

      responses:
        200:
          description: Response about conversation item deletion
          schema:
            type: object

definitions:
  DeleteConversationInfo:
    type: object
    required:
      - conversationItemId
      - recipients
    properties:
      conversationItemId:
        type: integer
        format: int64
      recipients:
        type: array
        items:
          type: number
          format: int64
