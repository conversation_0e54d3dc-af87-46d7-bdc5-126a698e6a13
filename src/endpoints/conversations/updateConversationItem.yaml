paths:
  /send-conversation-message/:conversationId:
    put:
      consumes:
        - application/json
      summary: Send conversation updated item to conversation
      tags:
        - conversations
      operationId: updateConversationItem
      parameters:
        - in: query
          name: conversationId
          description: pdated Conversation ID to send in
          type: integer
          required: true
          format: int64
        - in: body
          name: conversationItem
          description: Updated ConversationItem to send
          required: true
          schema:
            type: object
            $ref: '#/definitions/UpdatedConversationItem'
      responses:
        200:
          description: Response about sending conversation message
          schema:
            type: object

definitions:
  UpdatedConversationItem:
    type: object
    required:
      - id
      - content
      - updatedAt
      - recipients
    properties:
      id:
        type: integer
        format: int64
      content:
        type: string
      parentId:
        type: [integer, 'null']
        format: int64
      updatedAt:
        type: string
        format: date-time
      recipients:
        type: array
        items:
          type: number
          format: int64
