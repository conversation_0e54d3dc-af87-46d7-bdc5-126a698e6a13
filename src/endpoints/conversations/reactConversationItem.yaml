paths:
  /send-conversation-message/:conversationId/:conversationItemId/react:
    post:
      consumes:
        - application/json
      summary: React Conversation Item
      tags:
        - conversations
      operationId: reactConversationItem
      parameters:
        - in: query
          name: conversationId
          description: Conversation ID to send in
          type: integer
          required: true
          format: int64
        - in: body
          name: reactConversationInfo
          description: Short react conversation item info
          required: true
          schema:
            type: object
            $ref: '#/definitions/ReactConversationInfo'

      responses:
        200:
          description: Response about conversation item reaction
          schema:
            type: object

definitions:
  ReactConversationInfo:
    type: object
    required:
      - reactionType
      - participantPersonId
      - conversationItemId
      - recipients
    properties:
      participantPersonId:
        type: string
      reactionType:
        type: string
        enum: [LIKE, UNLIKE, STAR, UNSTAR]
      conversationItemId:
        type: integer
        format: int64
      recipients:
        type: array
        items:
          type: number
          format: int64
