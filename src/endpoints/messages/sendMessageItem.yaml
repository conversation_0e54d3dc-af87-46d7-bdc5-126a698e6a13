paths:
  /send-message/:messageId:
    post:
      consumes:
        - application/json
      summary: Send message item to chat
      tags:
        - messages
      operationId: sendMessageItem
      parameters:
        - in: query
          name: messageId
          description: Message ID to send in
          type: integer
          required: true
          format: int64
        - in: body
          name: messageItem
          description: completed MessageItem to send
          required: true
          schema:
            type: object
            $ref: '#/definitions/MessageItem'
      responses:
        200:
          description: Response about sending message
          schema:
            type: object

definitions:
  MessageItem:
    type: object
    required:
      - id
      - createdAt
      - itemType
      - __typename
    properties:
      id:
        type: integer
        format: int64
      content:
        type: [string, 'null']
      createdAt:
        type: string
        format: date-time
      itemType:
        type: string
        enum: [MESSAGE, ACTIVITY]
      status:
        type: string
        enum: [ACTIVE, INACTIVE, DELETED]
      createdByParticipant:
        type: object
        $ref: '#/definitions/MessageParticipant'
      attachments:
        type: array
        items:
          type: object
          $ref: '#/definitions/FileAttachment'
      parentMessageItem:
        type: object
        $ref: '#/definitions/MessageItem'
      activityPayload:
        type: [object, 'null']
        $ref: '#/definitions/ActivityPayload'
      recipients:
        type: array
        items:
          type: number
          format: int64
      __typename:
        type: string
        enum: [MessageItem]

  MessageParticipant:
    type: object
    required:
      - id
      - person
      - __typename
    properties:
      id:
        type: number
        format: int64
      person:
        type: object
        $ref: '#/definitions/Person'
      __typename:
        type: string
        enum: [MessageParticipant]

  FileAttachment:
    type: object
    required:
      - id
      - referenceId
      - fileId
      - sequence
      - status
      - fileMetadataId
      - version
      - fileName
      - fileSize
      - mimeType
      - url
      - __typename
    properties:
      id:
        type: number
        format: int64
      referenceId:
        type: number
        format: int64
      fileId:
        type: string
      description:
        type: [string, 'null']
      sequence:
        type: number
        format: int64
      status:
        type: string
        enum: [A, D]
      fileMetadataId:
        type: number
        format: int64
      version:
        type: number
        format: int64
      fileName:
        type: string
      fileSize:
        type: number
        format: int64
      mimeType:
        type: string
      url:
        type: string
      __typename:
        type: string
        enum: [FileAttachment]

  ActivityPayload:
    type: [object, 'null']
    required:
      - __typename
    properties:
      name:
        type: string
      author:
        type: string
      batch:
        type: string
      __typename:
        type: string
        enum: [ActivityPayload]

  Person:
    type: object
    required:
      - id
      - firstName
      - lastName
      - gender
      - tenantId
      - __typename
    properties:
      id:
        type: string
      fullName:
        type: string
      firstName:
        type: string
      lastName:
        type: string
      gender:
        type: string
        enum: [MALE, FEMALE]
      photoFileId:
        type: [string, 'null']
        format: int64
      birthDate:
        type: string
        format: date-time
      isOnline:
        type: string
        enum: [ONLINE, OFFLINE, UNAVAILABLE]
      preferredName:
        type: [string, 'null']
      tenantId:
        type: number
        format: int64
      organisationGroupId:
        type: number
        format: int64
      titleId:
        type: number
        format: int64
      __typename:
        type: string
        enum: [Person]
