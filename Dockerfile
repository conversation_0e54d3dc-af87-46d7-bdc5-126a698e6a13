FROM node:16.20.2-bullseye

ARG OAUTH_TOKEN

ADD ./lib /opt/oracle
RUN apt-get update && \
  apt upgrade -y && \
  apt-get install -y curl && \
  apt-get install -y libaio-dev && \
  apt-get install -y unzip && \
  apt clean && \
  rm -rf /var/lib/apt/lists/*
RUN cd /opt/oracle && \
  unzip instantclient.zip && \
  cd ./instantclient && \
  ln -s libclntsh.so.12.1 libclntsh.so && \
  rm -f /opt/oracle/instantclient.zip
WORKDIR /app
ADD . /app
ENV LD_LIBRARY_PATH /opt/oracle/instantclient:$LD_LIBRARY_PATH
RUN git config --global url."https://${OAUTH_TOKEN}@gitlab2.ed-space.net/".insteadOf "https://gitlab2.ed-space.net/"
RUN yarn
EXPOSE 4450
HEALTHCHECK --interval=10s --timeout=5s --start-period=30s \
    CMD curl -s localhost:4450/telemetry/health | if [ $(jq -r ".dbConnection") != online ]; then exit 1; fi || exit 1

CMD ["npm", "run", "start:docker"]
