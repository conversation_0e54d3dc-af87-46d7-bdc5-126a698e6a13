{"name": "edana2_api_notf", "version": "2.0.7", "description": "Web Socket notifications transport for Edana", "main": "index.js", "engines": {"node": "16.20.2"}, "scripts": {"start": "node $NODE_DEBUG_OPTION src/index.js", "start:docker": "npm start", "dev": "nodemon src/index.js", "build:dev": "npm run build", "build:stage2": "npm run build", "build:prod": "npm run build", "build:prod2": "npm run build", "lint": "eslint ./src", "test": "TEST_ENV=true mocha --exit './test/**/*.spec.js'", "test:coverage": "TEST_ENV=true nyc --all --reporter=html mocha --exit './test/**/*.spec.js'"}, "husky": {"hooks": {"pre-commit": "pretty-quick --staged && eslint ./src"}}, "repository": {"type": "git", "url": "git+https://gitlab2.ed-space.net/ed-admin/edana2_api_notf.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://gitlab2.ed-space.net/ed-admin/edana2_api_notf/issues"}, "homepage": "https://gitlab2.ed-space.net/ed-admin/edana2_api_notf#readme", "dependencies": {"bull": "^3.18.0", "cookies": "0.7.3", "dotenv": "^6.0.0", "edana-microservice": "git+https://gitlab2.ed-space.net/ed-admin/edana-microservice#6.8.2", "jsonwebtoken": "^8.5.1", "koa-socket-2": "^1.2.0", "lodash": "^4.17.20", "redis": "^2.8.0", "socket.io-redis": "^5.4.0", "uuid": "^3.3.3"}, "devDependencies": {"@types/node": "13.13.9", "babel-eslint": "^8.2.3", "chai": "^4.2.0", "eslint": "^4.19.1", "eslint-config-edana-node": "git+https://gitlab2.ed-space.net/ed-admin/eslint-config-edana-node.git#20.0.0", "husky": "^1.0.0-rc.2", "mocha": "^6.2.2", "mock-require": "^3.0.3", "nodemon": "^1.17.3", "nyc": "^14.1.1", "prettier": "^1.12.1", "pretty-quick": "^1.4.1", "sinon": "^7.5.0"}, "overrides": {"types-ramda": "0.29.4"}}