image: docker:stable

stages:
  - test
  - build
  - push
  - deploy

variables:
  IMAGE_TAG: ${CI_PROJECT_NAME}:${CI_COMMIT_SHORT_SHA}
  REPOSITORY_URL: 968411683265.dkr.ecr.eu-central-1.amazonaws.com

before_script:
  - echo "http://dl-cdn.alpinelinux.org/alpine/v3.12/community" >> /etc/apk/repositories
  - apk update
  - apk add aws-cli
  - aws configure set aws_access_key_id $AWS_KEY
  - aws configure set aws_secret_access_key $AWS_SECRET
  - $(aws ecr get-login --no-include-email --region eu-central-1)

unit_tests:
  stage: test
  image: node:16.20.2-alpine
  before_script:
    - echo "http://dl-cdn.alpinelinux.org/alpine/v3.12/community" >> /etc/apk/repositories
    - apk update
    - apk add aws-cli git
    - git config --global url."https://${CI_USER}:${CI_PASSWORD}@gitlab2.ed-space.net/".insteadOf "https://gitlab2.ed-space.net/"
    - aws configure set aws_access_key_id $AWS_KEY
    - aws configure set aws_secret_access_key $AWS_SECRET
    - npm install
  script:
    - 'case ${CI_COMMIT_REF_NAME} in dev ) export BUILD_ENV=dev;; stage ) export BUILD_ENV=stage2;; master2 ) export BUILD_ENV=prod2;;esac'
    #    - aws s3 cp s3://edana2-configs/${BUILD_ENV}/${CI_PROJECT_NAME}_tests.env ./.env
    - npm test
    - npm run lint
  only:
    - stage
  tags:
    - edana2-build

build:
  stage: build
  script:
    - 'docker pull ${REPOSITORY_URL}/${CI_PROJECT_NAME}:latest || true'
    - docker build -t ${REPOSITORY_URL}/${IMAGE_TAG} --build-arg OAUTH_TOKEN=${CI_USER}:${CI_PASSWORD} --cache-from ${REPOSITORY_URL}/${CI_PROJECT_NAME}:latest .
    - docker push ${REPOSITORY_URL}/${IMAGE_TAG}
  only:
    - dev
    - master2
    - design
    - stage
    - tags
  tags:
    - edana2-build

push branch:
  variables:
    GIT_STRATEGY: none
  stage: push
  script:
    - docker pull ${REPOSITORY_URL}/${IMAGE_TAG}
    - docker tag ${REPOSITORY_URL}/${IMAGE_TAG} ${REPOSITORY_URL}/${CI_PROJECT_NAME}:${CI_COMMIT_REF_NAME}
    - docker push ${REPOSITORY_URL}/${CI_PROJECT_NAME}:${CI_COMMIT_REF_NAME}
  only:
    - dev
    - master2
    - design
    - stage
  tags:
    - edana2-build

push tag:
  variables:
    GIT_STRATEGY: none
  stage: push
  script:
    - docker pull ${REPOSITORY_URL}/${IMAGE_TAG}
    - docker tag ${REPOSITORY_URL}/${IMAGE_TAG} ${REPOSITORY_URL}/${CI_PROJECT_NAME}:${CI_COMMIT_TAG}
    - docker push ${REPOSITORY_URL}/${CI_PROJECT_NAME}:${CI_COMMIT_TAG}
  only:
    - tags
  tags:
    - edana2-build

push latest:
  variables:
    GIT_STRATEGY: none
  stage: push
  script:
    - docker pull ${REPOSITORY_URL}/${IMAGE_TAG}
    - docker tag ${REPOSITORY_URL}/${IMAGE_TAG} ${REPOSITORY_URL}/${CI_PROJECT_NAME}:latest
    - docker push ${REPOSITORY_URL}/${CI_PROJECT_NAME}:latest
  only:
    - stage
    - master2
  tags:
    - edana2-build

deploy:
  variables:
    GIT_STRATEGY: none
  stage: deploy
  script:
    - $(aws ecr get-login --no-include-email --region eu-central-1)
    - 'case ${CI_COMMIT_REF_NAME} in dev ) export BUILD_ENV=dev;; stage ) export BUILD_ENV=stage2;; master2 ) export BUILD_ENV=prod2;;esac'
    - 'case ${CI_COMMIT_REF_NAME} in dev ) export PORT=4451;; stage ) export PORT=4451;; master2 ) export PORT=4452;;esac'
    - docker system prune -f
    - docker rm -f ${CI_PROJECT_NAME}_${BUILD_ENV} || true
    - aws s3 cp s3://edana2-configs/${BUILD_ENV}/${CI_PROJECT_NAME}.env .
    - docker run -d --name ${CI_PROJECT_NAME}_${BUILD_ENV} -p ${PORT}:4450 --link rabbit-${BUILD_ENV}:rabbitmq-${BUILD_ENV}  --restart unless-stopped --env-file ${CI_PROJECT_NAME}.env --network="root_${BUILD_ENV}" ${REPOSITORY_URL}/${IMAGE_TAG}
  only:
    - dev
  tags:
    - edana2-dev-docker

deploy_stage:
  variables:
    GIT_STRATEGY: none
  stage: deploy
  script:
    - $(aws ecr get-login --no-include-email --region eu-central-1)
    - 'case ${CI_COMMIT_REF_NAME} in dev ) export BUILD_ENV=dev;; stage ) export BUILD_ENV=stage2;; master2 ) export BUILD_ENV=prod2;;esac'
    - 'case ${CI_COMMIT_REF_NAME} in dev ) export PORT=4451;; stage ) export PORT=4451;; master2 ) export PORT=4452;;esac'
    - docker system prune -f
    - docker rm -f ${CI_PROJECT_NAME}_${BUILD_ENV} || true
    - aws s3 cp s3://edana2-configs/${BUILD_ENV}/${CI_PROJECT_NAME}.env .
    - docker run -d --name ${CI_PROJECT_NAME}_${BUILD_ENV} -p ${PORT}:4450 --link rabbit-${BUILD_ENV}:rabbitmq-${BUILD_ENV}  --restart unless-stopped --env-file ${CI_PROJECT_NAME}.env --network="root_${BUILD_ENV}" ${REPOSITORY_URL}/${IMAGE_TAG}
  only:
    - stage
  tags:
    - edana2-stage2-docker

deploy_prod:
  variables:
    GIT_STRATEGY: none
  stage: deploy
  script:
    - $(aws ecr get-login --no-include-email --region eu-central-1)
    - 'case ${CI_COMMIT_REF_NAME} in dev ) export BUILD_ENV=dev;; stage ) export BUILD_ENV=stage2;; master2 ) export BUILD_ENV=prod2;;esac'
    - 'case ${CI_COMMIT_REF_NAME} in dev ) export PORT=4451;; stage ) export PORT=4451;; master2 ) export PORT=4452;;esac'
    - docker system prune -f
    - docker rm -f ${CI_PROJECT_NAME}_${BUILD_ENV} || true
    - aws s3 cp s3://edana2-configs/${BUILD_ENV}/${CI_PROJECT_NAME}.env .
    - docker run -d --name ${CI_PROJECT_NAME}_${BUILD_ENV} -p ${PORT}:4450 --restart unless-stopped --env-file ${CI_PROJECT_NAME}.env --network="root_${BUILD_ENV}" ${REPOSITORY_URL}/${IMAGE_TAG}
  only:
    - master2
  tags:
    - edana2-prod2-docker
