const { noop } = require('lodash')
const { describe, it, beforeEach, afterEach } = require('mocha')
const mock = require('mock-require')
const sinon = require('sinon')
require('chai').should()

describe('Online checker', () => {
  const socket = { to: noop }

  let sandbox
  let getOnline
  let context = {}

  let removeUser
  let addUser
  let getUser

  before(() => {
    mock('../../src/conf', {})
    ;({
      removeUser,
      addUser,
      getUser,
    } = require('../../src/store/remote/socketStore'))
  })

  after(function() {
    mock.stopAll()
  })

  beforeEach(async () => {
    sandbox = sinon.createSandbox()
    ;({ getOnline } = require('../../src/store/services/online'))

    await addUser(123123, 'socket id 1')
    await addUser(123124, 'socket id 2')

    context = {
      ctx: {
        app: {
          io: socket,
        },
      },
    }

    const to = sandbox.stub(socket, 'to')
    to.withArgs('socket id 1')
      .returns({ to, clients: fn => fn(null, ['socket id 1']) })
      .withArgs('socket id 2')
      .returns({ to, clients: fn => fn(null, ['socket id 2']) })
  })

  afterEach(async () => {
    sandbox.restore()

    await removeUser(123123, 'socket id 1')
    await removeUser(123124, 'socket id 2')
    await removeUser(123125, 'socket id 3')
    await removeUser(123125, 'socket id 4')
  })

  it('should return online for correct user', async () => {
    const onlines = await getOnline(context, { userIds: [123123, 123124] })

    onlines[123123].should.be.true
    onlines[123124].should.be.true
  })

  it('should return offline for disconnected user', async () => {
    await removeUser(123124, 'socket id 2')
    const onlines = await getOnline(context, { userIds: [123123, 123124] })

    onlines[123123].should.be.true
    onlines[123124].should.be.false
  })

  it('should return offline for incorrect user', async () => {
    const onlines = await getOnline(context, { userIds: [123125] })

    onlines[123125].should.be.false
  })

  it('should invalidate inactive socket', async () => {
    await addUser(123125, 'socket id 3')
    await addUser(123125, 'socket id 4')

    socket.to
      .withArgs('socket id 3')
      .returns({ to: socket.to, clients: fn => fn(null, ['socket id 3']) })
      .withArgs('socket id 4')
      .returns({ to: socket.to, clients: fn => fn(null, ['socket id 3']) })

    const socketsBefore = await getUser(123125)
    socketsBefore.should.be.deep.equal(['socket id 3', 'socket id 4'])

    const onlines = await getOnline(context, { userIds: [123125] })
    onlines[123125].should.be.true

    const socketsAfter = await getUser(123125)
    socketsAfter.should.be.deep.equal(['socket id 3'])
  })
})
