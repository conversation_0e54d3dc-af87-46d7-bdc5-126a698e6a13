//TODO temporary solution, need to discuss usage library like proxyquire
function fakeDefaultExport(moduleRelativePath, stubs) {
  if (require.cache[require.resolve(moduleRelativePath)]) {
    delete require.cache[require.resolve(moduleRelativePath)]
  }
  Object.keys(stubs).forEach(dependencyRelativePath => {
    require.cache[require.resolve(dependencyRelativePath)] = {
      exports: stubs[dependencyRelativePath],
    }
  })

  return Object.assign(require(moduleRelativePath), {
    restore: () => {
      delete require.cache[require.resolve(moduleRelativePath)]
      Object.keys(stubs).forEach(dependencyRelativePath => {
        delete require.cache[require.resolve(dependencyRelativePath)]
      })
    },
  })
}

function restoreStubsInObject(stubsObject) {
  Object.keys(stubsObject).forEach(key => stubsObject[key].restore())
}

module.exports = {
  fakeDefaultExport,
  restoreStubsInObject,
  removeExtraSpaces: value =>
    value.replace(/[\r\n]+/g, ' ').replace(/\s\s+/g, ' '),
}
