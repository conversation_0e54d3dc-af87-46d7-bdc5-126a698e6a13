const { DateTime } = require('luxon')
module.exports = {
  notfAttachment: {
    cooked: {
      id: 1,
      notificationId: 2,
      fileId: 222,
    },
    raw: {
      ID: 1,
      NOTIFICATION_ID: 2,
      FILE_ID: 222,
    },
  },
  notification: {
    cooked: {
      id: 2,
      createdAt: DateTime.fromJSDate(new Date('2019-11-07')),
      senderId: 302319,
      categoryId: 1,
      subject: 'Sample subject',
      icon: 'server',
    },
    raw: {
      ID: 2,
      CREATED_AT: new Date('2019-11-07'),
      SENDER_ID: 302319,
      CATEGORY_ID: 1,
      SUBJECT: 'Sample subject',
      ICON: 'server',
    },
  },
  recipient: {
    cooked: {
      id: 1,
      personId: 302319,
      notificationId: 2,
      createdAt: DateTime.fromJSDate(new Date('2019-11-07')),
      readAt: DateTime.fromJSDate(new Date('2019-11-07')),
      content: 'Sample content',
    },
    raw: {
      ID: 1,
      PERSON_ID: 302319,
      NOTIFICATION_ID: 2,
      CREATED_AT: new Date('2019-11-07'),
      READ_AT: new Date('2019-11-07'),
      CONTENT: 'Sample content',
    },
  },
}
