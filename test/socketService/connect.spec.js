const { describe, it, beforeEach, afterEach, before, after } = require('mocha')
const mock = require('mock-require')
const sinon = require('sinon')
const chai = require('chai')
const { noop } = require('lodash')

chai.should()

describe('Connect/Disconnect', () => {
  const socket = { to: noop }

  let sandbox
  let context
  let onConnection
  let onDisconnect
  let isConnected

  let getUser
  let removeUser

  before(() => {
    mock('../../src/conf', {})
    ;({ getUser, removeUser } = require('../../src/store/remote/socketStore'))
  })

  after(() => {
    mock.stopAll()
  })

  beforeEach(() => {
    sandbox = sinon.createSandbox()
    ;({
      onConnection,
      onDisconnect,
      isConnected,
    } = require('../../src/store/services/socket'))

    context = {
      state: {
        log: { info: noop },
        userId: 123123,
      },
      socket: {
        id: 'socket id 1',
      },
    }

    const to = sandbox.stub(socket, 'to')
    const clients = ['socket id 1', 'socket id 2']
    to.withArgs('socket id 1')
      .returns({ to, clients: fn => fn(null, clients) })
      .withArgs('socket id 2')
      .returns({ to, clients: fn => fn(null, clients) })
  })

  afterEach(async () => {
    sandbox.restore()

    await removeUser(123123, 'socket id 1')
    await removeUser(123123, 'socket id 2')
  })

  it('should register user socket id', async () => {
    await onConnection(context, { id: 'socket id 1' })

    const sockets = await getUser(123123)
    sockets.should.be.deep.equal(['socket id 1'])

    const isOnline = await isConnected(socket, 123123)
    isOnline.should.be.true
  })

  it('should register both user socket ids', async () => {
    await onConnection(context, { id: 'socket id 1' })
    await onConnection(context, { id: 'socket id 2' })

    const sockets = await getUser(123123)
    sockets.should.be.deep.equal(['socket id 1', 'socket id 2'])

    const isOnline = await isConnected(socket, 123123)
    isOnline.should.be.true
  })

  it(`should have uniq socket ids for user`, async () => {
    await onConnection(context, { id: 'socket id 1' })
    await onConnection(context, { id: 'socket id 2' })
    await onConnection(context, { id: 'socket id 2' })
    await onConnection(context, { id: 'socket id 2' })

    const sockets = await getUser(123123)
    sockets.should.be.deep.equal(['socket id 1', 'socket id 2'])

    const isOnline = await isConnected(socket, 123123)
    isOnline.should.be.true
  })

  it(`shouldn't have user connected after disconnect`, async () => {
    await onConnection(context, { id: 'socket id 1' })
    await onDisconnect(context, '')

    const sockets = await getUser(123123)
    chai.assert.isNull(sockets)

    const isOnline = await isConnected(socket, 123123)
    isOnline.should.be.false
  })

  it('should disconnect only one socket', async () => {
    await onConnection(context, { id: 'socket id 1' })
    await onConnection(context, { id: 'socket id 2' })
    await onDisconnect(context, '')

    const sockets = await getUser(123123)
    sockets.should.be.deep.equal(['socket id 2'])

    const isOnline = await isConnected(socket, 123123)
    isOnline.should.be.true
  })
})
