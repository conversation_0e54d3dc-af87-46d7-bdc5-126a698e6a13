const { describe, it, beforeEach, afterEach, before, after } = require('mocha')
const mock = require('mock-require')
const sinon = require('sinon')
const chai = require('chai')
const { noop } = require('lodash')

chai.should()

describe(`Send message`, () => {
  const socket = { to: noop }
  let clients

  let sandbox
  let context
  let emit
  let to

  let send

  let removeUser
  let addUser
  let getUser

  before(() => {
    mock('../../src/conf', {})
    ;({
      removeUser,
      addUser,
      getUser,
    } = require('../../src/store/remote/socketStore'))
  })

  after(function() {
    mock.stopAll()
  })

  beforeEach(async () => {
    sandbox = sinon.createSandbox()

    emit = sandbox.stub()

    to = sandbox.stub(socket, 'to')
    clients = ['socket id 1', 'socket id 2']
    to.withArgs('socket id 1')
      .returns({ emit, to, clients: fn => fn(null, clients) })
      .withArgs('socket id 2')
      .returns({ emit, to, clients: fn => fn(null, clients) })
      .withArgs('socket id 3')
      .returns({ emit, to, clients: fn => fn(null, clients) })
      .withArgs('socket id 4')
      .returns({ emit, to, clients: fn => fn(null, clients) })

    context = { ctx: { app: { io: socket } }, log: { info: noop } }

    await addUser(123123, 'socket id 1')
    await addUser(123124, 'socket id 2')
    ;({ send } = require('../../src/store/services/notifications'))
  })

  afterEach(async () => {
    sandbox.restore()

    await removeUser(123123, 'socket id 1')
    await removeUser(123124, 'socket id 2')
    await removeUser(123123, 'socket id 3')
    await removeUser(123123, 'socket id 4')
  })

  it('should send message to online user', async () => {
    const notification = { asd: 'asd', recipientId: 123123 }
    await send(context, notification)

    sinon.assert.calledOnce(emit)
    sinon.assert.calledWith(emit.getCall(0), 'notification', notification)
  })

  it('should send message to online user all sockets', async () => {
    await addUser(123123, 'socket id 3')
    clients.push('socket id 3')

    const notification = { asd: 'asd', recipientId: 123123 }
    await send(context, notification)

    sinon.assert.callCount(to, 4)

    sinon.assert.calledOnce(emit)
    sinon.assert.calledWith(emit.getCall(0), 'notification', notification)
  })

  it(`shouldn't send message to offline user`, async () => {
    const notification = { asd: 'asd' }
    await send(context, notification)

    sinon.assert.notCalled(emit)
  })

  it(`should invalidate inactive sockets`, async () => {
    // TODO: implement
  })
})
